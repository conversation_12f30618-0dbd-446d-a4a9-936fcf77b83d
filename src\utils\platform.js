import { Platform, Dimensions, StatusBar } from 'react-native';
import Constants from 'expo-constants';
import * as Device from 'expo-device';

// Platform Detection
export const isIOS = Platform.OS === 'ios';
export const isAndroid = Platform.OS === 'android';
export const isWeb = Platform.OS === 'web';

// Device Information
export const getDeviceInfo = () => {
  return {
    platform: Platform.OS,
    version: Platform.Version,
    isDevice: Device.isDevice,
    deviceName: Device.deviceName,
    deviceType: Device.deviceType,
    modelName: Device.modelName,
    osName: Device.osName,
    osVersion: Device.osVersion,
    platformApiLevel: Device.platformApiLevel,
    manufacturer: Device.manufacturer,
    brand: Device.brand,
    designName: Device.designName,
    productName: Device.productName,
    deviceYearClass: Device.deviceYearClass,
    totalMemory: Device.totalMemory
  };
};

// Screen Dimensions
export const getScreenDimensions = () => {
  const { width, height } = Dimensions.get('window');
  const screenData = Dimensions.get('screen');
  
  return {
    window: { width, height },
    screen: screenData,
    isLandscape: width > height,
    isPortrait: height > width,
    aspectRatio: width / height
  };
};

// Safe Area Calculations
export const getSafeAreaInsets = () => {
  const statusBarHeight = Constants.statusBarHeight || 0;
  
  return {
    top: statusBarHeight,
    bottom: isIOS ? 34 : 0, // iPhone X+ bottom safe area
    left: 0,
    right: 0
  };
};

// Device Type Detection
export const getDeviceType = () => {
  const { width, height } = Dimensions.get('window');
  const aspectRatio = height / width;
  
  if (isIOS) {
    // iPhone detection based on screen dimensions
    if (width === 375 && height === 812) return 'iPhone X/XS/11 Pro';
    if (width === 414 && height === 896) return 'iPhone XR/XS Max/11/11 Pro Max';
    if (width === 390 && height === 844) return 'iPhone 12/12 Pro/13/13 Pro';
    if (width === 428 && height === 926) return 'iPhone 12 Pro Max/13 Pro Max';
    if (width === 375 && height === 667) return 'iPhone 6/6S/7/8/SE 2nd';
    if (width === 414 && height === 736) return 'iPhone 6+/6S+/7+/8+';
    if (width === 320 && height === 568) return 'iPhone 5/5S/5C/SE 1st';
    
    // iPad detection
    if (Device.deviceType === Device.DeviceType.TABLET) {
      return 'iPad';
    }
    
    return 'iPhone';
  }
  
  if (isAndroid) {
    // Android device type detection
    if (Device.deviceType === Device.DeviceType.TABLET) {
      return 'Android Tablet';
    }
    
    // Screen size based detection
    if (width >= 600) return 'Android Tablet';
    if (aspectRatio > 2) return 'Android Phone (Tall)';
    
    return 'Android Phone';
  }
  
  return 'Unknown';
};

// Check if device has notch/dynamic island
export const hasNotch = () => {
  if (!isIOS) return false;
  
  const { height, width } = Dimensions.get('window');
  const statusBarHeight = Constants.statusBarHeight;
  
  // iPhone X and newer have status bar height > 20
  return statusBarHeight > 20;
};

// Check if device supports biometric authentication
export const supportsBiometrics = () => {
  // This would typically use expo-local-authentication
  // For now, return true for modern devices
  return Device.deviceYearClass >= 2017;
};

// Get status bar height
export const getStatusBarHeight = () => {
  if (isAndroid) {
    return StatusBar.currentHeight || 0;
  }
  
  return Constants.statusBarHeight || (hasNotch() ? 44 : 20);
};

// Check if device is in dark mode
export const isDarkMode = () => {
  // This would typically use react-native-appearance or expo-appearance
  // For now, return false as default
  return false;
};

// Platform-specific styling helpers
export const platformStyles = {
  shadow: isIOS ? {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  } : {
    elevation: 4,
  },
  
  headerHeight: isIOS ? 44 : 56,
  tabBarHeight: isIOS ? (hasNotch() ? 83 : 49) : 56,
  
  borderRadius: isIOS ? 8 : 4,
  
  fontFamily: isIOS ? 'System' : 'Roboto',
  
  buttonHeight: isIOS ? 44 : 48,
  
  inputHeight: isIOS ? 44 : 48,
};

// Haptic feedback helpers
export const hapticFeedback = {
  light: () => {
    // Implementation would use expo-haptics
    console.log('Light haptic feedback');
  },
  
  medium: () => {
    // Implementation would use expo-haptics
    console.log('Medium haptic feedback');
  },
  
  heavy: () => {
    // Implementation would use expo-haptics
    console.log('Heavy haptic feedback');
  },
  
  success: () => {
    // Implementation would use expo-haptics
    console.log('Success haptic feedback');
  },
  
  warning: () => {
    // Implementation would use expo-haptics
    console.log('Warning haptic feedback');
  },
  
  error: () => {
    // Implementation would use expo-haptics
    console.log('Error haptic feedback');
  }
};

// Network information
export const getNetworkInfo = async () => {
  // This would typically use @react-native-community/netinfo
  return {
    isConnected: true,
    type: 'wifi',
    isInternetReachable: true
  };
};

// Battery information
export const getBatteryInfo = async () => {
  // This would typically use expo-battery
  return {
    batteryLevel: 1.0,
    batteryState: 'unknown',
    lowPowerMode: false
  };
};

export default {
  isIOS,
  isAndroid,
  isWeb,
  getDeviceInfo,
  getScreenDimensions,
  getSafeAreaInsets,
  getDeviceType,
  hasNotch,
  supportsBiometrics,
  getStatusBarHeight,
  isDarkMode,
  platformStyles,
  hapticFeedback,
  getNetworkInfo,
  getBatteryInfo
};
