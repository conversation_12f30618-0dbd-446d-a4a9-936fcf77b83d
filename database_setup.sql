-- SUPABASE SQL COMMANDS FOR REVIEWS TABLE
-- Execute these commands in the Supabase SQL Editor

-- 1. Create the reviews table
CREATE TABLE public.reviews (
  id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  content text, -- <PERSON>rum metni
  rating numeric(2, 1) NOT NULL, -- Kullanıcının mekana verdiği puan (1.0 - 5.0)
  venue_id bigint REFERENCES public.venues(id) ON DELETE CASCADE, -- 'venues' tablosuna referans
  user_id uuid REFERENCES auth.users(id) ON DELETE SET NULL, -- <PERSON><PERSON><PERSON> referans
  author_name text -- <PERSON><PERSON><PERSON><PERSON> kull<PERSON>ı adını manuel ekleyec<PERSON>
);

-- 2. Disable RLS for development
ALTER TABLE public.reviews DISABLE ROW LEVEL SECURITY;

-- 3. Insert sample reviews
-- Note: Replace venue_id values (1, 2) with your actual venue IDs if different
INSERT INTO public.reviews (venue_id, rating, content, author_name) VALUES
(1, 5.0, '<PERSON><PERSON><PERSON><PERSON> harika, özellikle V60 demlemelerini tavsiye ederim. Ortam çok keyifli.', 'Ayşe Yılmaz'),
(1, 4.0, 'Çalışmak için ideal bir yer ama hafta sonları çok kalabalık olabiliyor.', 'Mehmet Kaya'),
(2, 4.5, 'Flat white içtim ve bayıldım. Baristalar çok bilgili ve ilgili.', 'Zeynep Demir');