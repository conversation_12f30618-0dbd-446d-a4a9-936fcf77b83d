{"expo": {"name": "SipTracker", "slug": "siptracker-clean", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "scheme": "siptracker", "description": "Track and discover amazing venues, rate your experiences, and find your next favorite spot.", "primaryColor": "#6366f1", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.siptracker.app", "buildNumber": "1", "requireFullScreen": false, "userInterfaceStyle": "automatic", "infoPlist": {"NSLocationWhenInUseUsageDescription": "Bu uygulama yakınınızdaki mekanları göstermek için konum bilginizi kullanır.", "NSLocationAlwaysAndWhenInUseUsageDescription": "Bu uygulama yakınınızdaki mekanları göstermek için konum bilginizi kullanır.", "NSCameraUsageDescription": "Bu uygulama profil fotoğrafı çekmek için kameraya erişim gerektirir.", "NSPhotoLibraryUsageDescription": "Bu uygulama profil fotoğrafı seçmek için fotoğraf galerisine erişim gerektirir.", "NSMicrophoneUsageDescription": "Bu uygulama ses kaydetmek için mikrofona eri<PERSON>im gere<PERSON>.", "NSContactsUsageDescription": "Bu uygulama arkadaşlarınızı bulmak için rehberinize erişim gerektirir.", "NSCalendarsUsageDescription": "Bu uygulama etkinlikleri takviminize eklemek için er<PERSON> gere<PERSON>.", "NSRemindersUsageDescription": "Bu uygulama hatırlatıcılar oluşturmak için erişim gerektirir.", "NSFaceIDUsageDescription": "Bu uygulama güvenli giriş için Face ID kullanır.", "ITSAppUsesNonExemptEncryption": false, "CFBundleAllowMixedLocalizations": true, "CFBundleLocalizations": ["tr", "en"]}, "associatedDomains": ["applinks:siptracker.app", "applinks:www.siptracker.app"], "config": {"usesNonExemptEncryption": false}}, "android": {"package": "com.siptracker.app", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "allowBackup": true, "permissions": ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "READ_MEDIA_IMAGES", "READ_MEDIA_VIDEO", "RECORD_AUDIO", "VIBRATE", "RECEIVE_BOOT_COMPLETED", "WAKE_LOCK", "INTERNET", "ACCESS_NETWORK_STATE", "ACCESS_WIFI_STATE", "READ_CONTACTS", "USE_BIOMETRIC", "USE_FINGERPRINT"], "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "siptracker.app"}, {"scheme": "https", "host": "www.siptracker.app"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-font", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Bu uygulama yakınınızdaki mekanları göstermek için konum bilginizi kullanır.", "locationAlwaysPermission": "Bu uygulama yakınınızdaki mekanları göstermek için konum bilginizi kullanır.", "locationWhenInUsePermission": "Bu uygulama yakınınızdaki mekanları göstermek için konum bilginizi kullanır.", "isIosBackgroundLocationEnabled": false, "isAndroidBackgroundLocationEnabled": false}], ["expo-image-picker", {"photosPermission": "Bu uygulama profil fotoğrafı seçmek için fotoğraf galerisine erişim gerektirir.", "cameraPermission": "Bu uygulama profil fotoğrafı çekmek için kameraya erişim gerektirir."}], ["expo-notifications", {"color": "#6366f1"}], ["expo-camera", {"cameraPermission": "Bu uygulama fotoğraf çekmek için kameraya erişim gerektirir.", "microphonePermission": "Bu uygulama video kaydetmek için mikrofona eri<PERSON><PERSON> gere<PERSON>.", "recordAudioAndroid": true}], ["expo-media-library", {"photosPermission": "Bu uygulama fotoğrafları kaydetmek için medya kütüphanesine erişim gerektirir.", "savePhotosPermission": "Bu uygulama fotoğrafları kaydetmek için izin gerektirir.", "isAccessMediaLocationEnabled": true}], "expo-splash-screen", "expo-updates"], "extra": {"eas": {"projectId": "cc8b31a5-ed97-4155-a52d-48b89ade8fcf"}}, "owner": "rzgrozt"}}