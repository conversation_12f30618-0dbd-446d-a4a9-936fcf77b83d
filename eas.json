{"cli": {"version": ">= 12.0.0", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "m-medium"}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}}, "preview": {"distribution": "internal", "env": {"SUPABASE_URL": "https://xnkttsqnfduehouaggzr.supabase.co", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.DPLivfklW-QfHh-zVHaezZgm2Dh1fDk8XBBQNy_4h7s"}, "ios": {"resourceClass": "m-medium", "simulator": true}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "production": {"env": {"SUPABASE_URL": "https://xnkttsqnfduehouaggzr.supabase.co", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.DPLivfklW-QfHh-zVHaezZgm2Dh1fDk8XBBQNy_4h7s"}, "ios": {"resourceClass": "m-medium"}, "android": {"buildType": "apk"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-store-connect-app-id", "appleTeamId": "your-apple-team-id"}, "android": {"serviceAccountKeyPath": "./path/to/api-key.json", "track": "internal"}}}}