import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { Colors } from '../constants/Colors';

export default function VenueCardSkeleton() {
  const shimmerValue = useSharedValue(0);

  useEffect(() => {
    shimmerValue.value = withRepeat(
      withTiming(1, { duration: 1500 }),
      -1,
      false
    );
  }, []);

  const shimmerStyle = useAnimatedStyle(() => {
    const translateX = interpolate(
      shimmerValue.value,
      [0, 1],
      [-100, 400]
    );

    return {
      transform: [{ translateX }],
    };
  });

  return (
    <View style={styles.card}>
      <View style={styles.imageContainer}>
        <Animated.View style={[styles.shimmer, shimmerStyle]} />
      </View>
      <View style={styles.infoContainer}>
        <View style={styles.titleSkeleton}>
          <Animated.View style={[styles.shimmer, shimmerStyle]} />
        </View>
        <View style={styles.subtitleSkeleton}>
          <Animated.View style={[styles.shimmer, shimmerStyle]} />
        </View>
      </View>
      <View style={styles.ratingSkeleton}>
        <Animated.View style={[styles.shimmer, shimmerStyle]} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    marginHorizontal: 20,
    marginBottom: 20,
    overflow: 'hidden',
  },
  imageContainer: {
    width: '100%',
    height: 180,
    backgroundColor: '#2A2A2A',
    position: 'relative',
    overflow: 'hidden',
  },
  infoContainer: {
    padding: 16,
  },
  titleSkeleton: {
    height: 20,
    backgroundColor: '#2A2A2A',
    borderRadius: 4,
    marginBottom: 8,
    width: '70%',
    overflow: 'hidden',
    position: 'relative',
  },
  subtitleSkeleton: {
    height: 16,
    backgroundColor: '#2A2A2A',
    borderRadius: 4,
    width: '50%',
    overflow: 'hidden',
    position: 'relative',
  },
  ratingSkeleton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 60,
    height: 24,
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    overflow: 'hidden',
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    width: 100,
  },
});