import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '@env';

// Fallback values for production builds
const supabaseUrl = SUPABASE_URL || 'https://xnkttsqnfduehouaggzr.supabase.co';
const supabaseAnonKey = SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inhua3R0c3FuZmR1ZWhvdWFnZ3pyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNDYyMTksImV4cCI6MjA2NDcyMjIxOX0.DPLivfklW-QfHh-zVHaezZgm2Dh1fDk8XBBQNy_4h7s';

if (!supabaseUrl || !supabaseAnonKey) {
  console.error("Supabase URL veya Anahtar eksik. .env dosyanızı kontrol edin.");
  console.error("URL:", supabaseUrl);
  console.error("Key exists:", !!supabaseAnonKey);
}

console.log('🔧 Supabase configuration:', {
  url: supabaseUrl,
  hasKey: !!supabaseAnonKey,
  keyLength: supabaseAnonKey?.length || 0
});

export const supabase = createClient(supabaseUrl, supabaseAnonKey);