import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  runOnJS,
} from 'react-native-reanimated';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';

export default function CustomTabBar({ state, descriptors, navigation }) {
  const dropAnimationValues = state.routes.map(() => useSharedValue(-30));
  const dropOpacityValues = state.routes.map(() => useSharedValue(0));

  const getIconName = (routeName, focused) => {
    switch (routeName) {
      case 'Home':
        return focused ? 'home' : 'home-outline';
      case 'Explore':
        return focused ? 'compass' : 'compass-outline';
      case 'Search':
        return focused ? 'search' : 'search-outline';
      case 'Profile':
        return focused ? 'person' : 'person-outline';
      default:
        return 'ellipse';
    }
  };

  const getTabLabel = (routeName) => {
    switch (routeName) {
      case 'Home':
        return 'Ana Sayfa';
      case 'Explore':
        return 'Keşfet';
      case 'Search':
        return 'Ara';
      case 'Profile':
        return 'Profil';
      default:
        return routeName;
    }
  };

  const triggerDropAnimation = (index) => {
    // Reset animation values
    dropAnimationValues[index].value = -30;
    dropOpacityValues[index].value = 0;

    // Start the drop animation sequence
    dropAnimationValues[index].value = withSequence(
      withSpring(10, { damping: 8, stiffness: 100 }),
      withTiming(-30, { duration: 800 })
    );

    dropOpacityValues[index].value = withSequence(
      withTiming(1, { duration: 200 }),
      withTiming(1, { duration: 400 }),
      withTiming(0, { duration: 400 })
    );
  };

  useEffect(() => {
    const focusedIndex = state.index;
    triggerDropAnimation(focusedIndex);
  }, [state.index]);

  return (
    <View style={styles.tabBar}>
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const isFocused = state.index === index;

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        const dropStyle = useAnimatedStyle(() => ({
          transform: [{ translateY: dropAnimationValues[index].value }],
          opacity: dropOpacityValues[index].value,
        }));

        return (
          <TouchableOpacity
            key={route.key}
            style={styles.tabButton}
            onPress={onPress}
            activeOpacity={0.7}
          >
            <View style={styles.iconContainer}>
              {/* Coffee drop animation */}
              <Animated.View style={[styles.coffeeDrop, dropStyle]} />
              
              {/* Tab icon */}
              <Ionicons
                name={getIconName(route.name, isFocused)}
                size={24}
                color={isFocused ? Colors.tabIconSelected : Colors.tabIconDefault}
              />
            </View>
            
            <Text
              style={[
                styles.tabLabel,
                globalStyles.caption,
                { color: isFocused ? Colors.tabIconSelected : Colors.tabIconDefault }
              ]}
            >
              {getTabLabel(route.name)}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    backgroundColor: Colors.card,
    borderTopWidth: 0,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    paddingVertical: 8,
    paddingBottom: 20,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  iconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    height: 32,
    marginBottom: 4,
  },
  coffeeDrop: {
    position: 'absolute',
    width: 8,
    height: 8,
    backgroundColor: Colors.primary,
    borderRadius: 8,
    top: -15,
  },
  tabLabel: {
    fontSize: 11,
    textAlign: 'center',
  },
});