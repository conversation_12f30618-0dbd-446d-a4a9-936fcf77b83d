import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, Button, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import spacingScale from '../constants/spacing';

export default function AddReviewForm({ onSubmit, onCancel }) {
  const [ratingCoffee, setRatingCoffee] = useState(0);
  const [ratingVibe, setRatingVibe] = useState(0);
  const [ratingService, setRatingService] = useState(0);
  const [content, setContent] = useState('');

  const Star = ({ filled, onPress }) => (
    <TouchableOpacity onPress={onPress}>
      <Ionicons
        name={filled ? 'star' : 'star-outline'}
        size={24}
        color="#FFD700"
        style={{ marginRight: 8 }}
      />
    </TouchableOpacity>
  );

  const CategoryRating = ({ title, rating, setRating }) => (
    <View style={styles.categoryContainer}>
      <Text style={[styles.categoryTitle, globalStyles.bodySmall]}>{title}</Text>
      <View style={styles.starContainer}>
        {[1, 2, 3, 4, 5].map(i => (
          <Star
            key={i}
            filled={i <= rating}
            onPress={() => setRating(i)}
          />
        ))}
      </View>
    </View>
  );

  const handleSubmit = () => {
    if (ratingCoffee > 0 && ratingVibe > 0 && ratingService > 0 && content.length > 2) {
      const overallRating = (ratingCoffee + ratingVibe + ratingService) / 3;
      onSubmit({
        rating: overallRating,
        rating_coffee: ratingCoffee,
        rating_vibe: ratingVibe,
        rating_service: ratingService,
        content
      });
    } else {
      alert('Lütfen tüm kategoriler için puan verin ve en az 3 karakterlik bir yorum yazın.');
    }
  };

  return (
    <View style={styles.modalContainer}>
      <View style={styles.modalContent}>
        <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollContainer}>
          <Text style={[styles.title, globalStyles.subheading]}>Detaylı Değerlendirme</Text>

          <CategoryRating
            title="☕ Kahve Lezzeti"
            rating={ratingCoffee}
            setRating={setRatingCoffee}
          />

          <CategoryRating
            title="🏠 Mekan Atmosferi"
            rating={ratingVibe}
            setRating={setRatingVibe}
          />

          <CategoryRating
            title="👥 Servis Kalitesi"
            rating={ratingService}
            setRating={setRatingService}
          />

          <TextInput
            style={[styles.input, globalStyles.body]}
            placeholder="Mekan hakkındaki düşüncelerin..."
            placeholderTextColor="#888"
            multiline
            value={content}
            onChangeText={setContent}
          />

          <Button
            title="Yorumu Gönder"
            onPress={handleSubmit}
            color={Colors.primary}
          />
          <TouchableOpacity onPress={onCancel} style={styles.cancelButton}>
            <Text style={[styles.cancelText, globalStyles.bodySmall]}>İptal</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)'
  },
  modalContent: {
    backgroundColor: Colors.card,
    padding: spacingScale.lg,
    borderRadius: 12,
    width: '90%',
    maxHeight: '80%',
    alignItems: 'center'
  },
  scrollContainer: {
    width: '100%',
  },
  title: {
    color: Colors.text,
    marginBottom: spacingScale.lg,
    textAlign: 'center'
  },
  categoryContainer: {
    width: '100%',
    marginBottom: spacingScale.lg,
    alignItems: 'center'
  },
  categoryTitle: {
    color: Colors.text,
    marginBottom: spacingScale.sm,
    fontWeight: '600'
  },
  starContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacingScale.sm
  },
  input: {
    backgroundColor: Colors.background,
    color: Colors.text,
    borderRadius: 8,
    width: '100%',
    height: 100,
    padding: spacingScale.md,
    textAlignVertical: 'top',
    marginBottom: spacingScale.lg,
    borderWidth: 1,
    borderColor: Colors.border || '#E0E0E0'
  },
  cancelButton: {
    marginTop: spacingScale.md
  },
  cancelText: {
    color: Colors.tabIconDefault
  }
});