import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import VenueCard from '../components/VenueCard';
import VenueCardSkeleton from '../components/VenueCardSkeleton';
import EmptyState from '../components/EmptyState';
import ExploreWizard from '../components/ExploreWizard';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import spacingScale from '../constants/spacing';

export default function ExploreScreen({ navigation }) {
  const [venues, setVenues] = useState([]);
  const [originalVenues, setOriginalVenues] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [wizardVisible, setWizardVisible] = useState(false);
  const [activeFilters, setActiveFilters] = useState(null);

  useEffect(() => {
    const fetchVenues = async () => {
      try {
        setLoading(true);
        const { data, error: fetchError } = await supabase
          .from('venues')
          .select('*')
          .order('overall_rating', { ascending: false });

        if (fetchError) throw fetchError;

        setVenues(data || []);
        setOriginalVenues(data || []);
      } catch (e) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    };

    fetchVenues();
  }, []);

  const calculateMatchScore = (venue, weights) => {
    const coffeeScore = (venue.rating_coffee || 3.0) * weights.coffee;
    const vibeScore = (venue.rating_vibe || 3.0) * weights.vibe;
    const serviceScore = (venue.rating_service || 3.0) * weights.service;

    const totalWeight = weights.coffee + weights.vibe + weights.service;
    const maxPossibleScore = 5 * totalWeight;
    const actualScore = coffeeScore + vibeScore + serviceScore;

    return (actualScore / maxPossibleScore) * 100;
  };

  const handleApplyFilters = (weights) => {
    const venuesWithScores = originalVenues.map(venue => ({
      ...venue,
      matchScore: calculateMatchScore(venue, weights)
    }));

    const sortedVenues = venuesWithScores.sort((a, b) => b.matchScore - a.matchScore);
    setVenues(sortedVenues);
    setActiveFilters(weights);
  };

  const handleResetFilters = () => {
    setVenues(originalVenues);
    setActiveFilters(null);
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <FlatList
          data={[1, 2, 3, 4]} // Dummy data for skeleton loaders
          renderItem={() => <VenueCardSkeleton />}
          keyExtractor={(_, index) => `skeleton-${index}`}
          contentContainerStyle={{ paddingTop: spacingScale.lg, paddingBottom: spacingScale.lg }}
          showsVerticalScrollIndicator={false}
          initialNumToRender={4}
          maxToRenderPerBatch={2}
          windowSize={10}
        />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <EmptyState
          icon="alert-circle-outline"
          title="Bir Hata Oluştu"
          subtitle={`Mekanlar yüklenirken hata: ${error}`}
          style={{ flex: 1 }}
        />
      </SafeAreaView>
    );
  }

  if (venues.length === 0 && !loading) {
    return (
      <SafeAreaView style={styles.container}>
        <EmptyState
          icon="storefront-outline"
          title="Henüz Mekan Yok"
          subtitle="Yakında harika kahve mekanları eklenecek!"
          style={{ flex: 1 }}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header with Wizard Button */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.wizardButton}
          onPress={() => setWizardVisible(true)}
          activeOpacity={0.8}
        >
          <Ionicons name="options" size={20} color="white" />
          <Text style={[styles.wizardButtonText, globalStyles.body]}>
            🔮 Keşfet Sihirbazı
          </Text>
        </TouchableOpacity>

        {activeFilters && (
          <TouchableOpacity
            style={styles.resetButton}
            onPress={handleResetFilters}
            activeOpacity={0.7}
          >
            <Ionicons name="refresh" size={16} color={Colors.tabIconDefault} />
            <Text style={[styles.resetButtonText, globalStyles.bodySmall]}>
              Sıfırla
            </Text>
          </TouchableOpacity>
        )}
      </View>

      <Animated.FlatList
        data={venues}
        renderItem={({ item, index }) => (
          <Animated.View entering={FadeInDown.delay(index * 50)}>
            <VenueCard venue={item} navigation={navigation} showMatchScore={!!activeFilters} />
          </Animated.View>
        )}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={{
          paddingTop: spacingScale.sm,
          paddingBottom: spacingScale.md + 80 // Extra padding for tab bar
        }}
        showsVerticalScrollIndicator={false}
        initialNumToRender={6}
        maxToRenderPerBatch={4}
        windowSize={10}
        removeClippedSubviews={true}
      />

      <ExploreWizard
        visible={wizardVisible}
        onClose={() => setWizardVisible(false)}
        onApplyFilters={handleApplyFilters}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacingScale.lg,
    paddingVertical: spacingScale.md,
    backgroundColor: Colors.background,
  },
  wizardButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: spacingScale.lg,
    paddingVertical: spacingScale.md,
    borderRadius: 25,
    flex: 1,
    justifyContent: 'center',
  },
  wizardButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: spacingScale.sm,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    paddingHorizontal: spacingScale.md,
    paddingVertical: spacingScale.sm,
    borderRadius: 15,
    marginLeft: spacingScale.md,
  },
  resetButtonText: {
    color: Colors.tabIconDefault,
    marginLeft: spacingScale.xs,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background
  },
  errorText: {
    color: 'red'
  }
});