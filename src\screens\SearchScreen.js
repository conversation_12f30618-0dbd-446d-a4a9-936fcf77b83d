import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, FlatList, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import VenueCard from '../components/VenueCard';
import EmptyState from '../components/EmptyState';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import spacingScale from '../constants/spacing';

export default function SearchScreen({ navigation }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [venues, setVenues] = useState([]);
  const [filteredVenues, setFilteredVenues] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchVenues();
  }, []);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredVenues([]);
    } else {
      const filtered = venues.filter(venue =>
        venue.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        venue.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
        venue.address.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredVenues(filtered);
    }
  }, [searchQuery, venues]);

  const fetchVenues = async () => {
    try {
      const { data, error } = await supabase
        .from('venues')
        .select('*')
        .order('name');

      if (!error && data) {
        setVenues(data);
      }
    } catch (error) {
      console.error('Error fetching venues:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color={Colors.tabIconDefault} style={styles.searchIcon} />
        <TextInput
          style={[styles.searchInput, globalStyles.body]}
          placeholder="Mekan, şehir veya adres ara..."
          placeholderTextColor={Colors.tabIconDefault}
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoCapitalize="none"
          returnKeyType="search"
          clearButtonMode="while-editing"
        />
      </View>

      {loading ? (
        <View style={styles.centered}>
          <ActivityIndicator size="large" color={Colors.primary} />
        </View>
      ) : searchQuery.trim() === '' ? (
        <EmptyState
          icon="search"
          title="Arama Yapmaya Hazır"
          subtitle="Mekan, şehir veya adres aramak için yukarıdaki kutucuğu kullanın"
          style={{ flex: 1 }}
        />
      ) : filteredVenues.length === 0 ? (
        <EmptyState
          icon="sad-outline"
          title="Sonuç Bulunamadı"
          subtitle={`"${searchQuery}" için eşleşen mekan bulunamadı. Farklı anahtar kelimeler deneyin.`}
          style={{ flex: 1 }}
        />
      ) : (
        <FlatList
          data={filteredVenues}
          renderItem={({ item }) => <VenueCard venue={item} navigation={navigation} />}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={{
            paddingBottom: spacingScale.xl + 80 // Extra padding for tab bar
          }}
          showsVerticalScrollIndicator={false}
          initialNumToRender={8}
          maxToRenderPerBatch={4}
          windowSize={10}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    marginHorizontal: spacingScale.lg,
    marginTop: spacingScale.lg,
    marginBottom: spacingScale.md,
    borderRadius: 12,
    paddingHorizontal: spacingScale.lg,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    color: Colors.text,
    paddingVertical: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    color: Colors.tabIconDefault,
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 24,
  },
});