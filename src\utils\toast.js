import Toast from 'react-native-toast-message';

// Premium Toast Mesaj Sistemi
// Alert.alert() yerine kullanılaca<PERSON> zarif toast mesajları

export const showToast = {
  success: (title, message = '') => {
    Toast.show({
      type: 'success',
      text1: title,
      text2: message,
      visibilityTime: 3000,
      autoHide: true,
      topOffset: 60,
    });
  },

  error: (title, message = '') => {
    Toast.show({
      type: 'error',
      text1: title,
      text2: message,
      visibilityTime: 4000,
      autoHide: true,
      topOffset: 60,
    });
  },

  info: (title, message = '') => {
    Toast.show({
      type: 'info',
      text1: title,
      text2: message,
      visibilityTime: 3000,
      autoHide: true,
      topOffset: 60,
    });
  },

  warning: (title, message = '') => {
    Toast.show({
      type: 'warning',
      text1: title,
      text2: message,
      visibilityTime: 3500,
      autoHide: true,
      topOffset: 60,
    });
  },

  // Özel durumlar için
  custom: (config) => {
    Toast.show({
      topOffset: 60,
      autoHide: true,
      visibilityTime: 3000,
      ...config,
    });
  },
};

// Kısa kullanım için
export const toast = showToast;

export default showToast;
