import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import spacingScale from '../constants/spacing';

const StarRating = ({ rating }) => {
  const stars = [];
  for (let i = 1; i <= 5; i++) {
    stars.push(
      <Ionicons
        key={i}
        name={i <= rating ? 'star' : 'star-outline'}
        size={16}
        color="#FFD700"
      />
    );
  }
  return <View style={styles.starContainer}>{stars}</View>;
};

export default function ReviewCard({ review }) {
  const hasDetailedRatings = review.rating_coffee && review.rating_vibe && review.rating_service;

  return (
    <View style={styles.card}>
      <View style={styles.header}>
        <Text style={[styles.author, globalStyles.body]}>{review.author_name}</Text>
        <StarRating rating={review.rating} />
      </View>

      {/* Detailed Category Ratings */}
      {hasDetailedRatings && (
        <View style={styles.categoryRatings}>
          <View style={styles.categoryRow}>
            <Text style={styles.categoryLabel}>☕ Kahve</Text>
            <StarRating rating={review.rating_coffee} />
          </View>
          <View style={styles.categoryRow}>
            <Text style={styles.categoryLabel}>🏠 Atmosfer</Text>
            <StarRating rating={review.rating_vibe} />
          </View>
          <View style={styles.categoryRow}>
            <Text style={styles.categoryLabel}>👥 Servis</Text>
            <StarRating rating={review.rating_service} />
          </View>
        </View>
      )}

      <Text style={[styles.content, globalStyles.bodySmall]}>{review.content}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  card: { 
    backgroundColor: Colors.card, 
    borderRadius: 12, 
    padding: 16, 
    marginBottom: 15 
  },
  header: { 
    flexDirection: 'row', 
    justifyContent: 'space-between', 
    alignItems: 'center', 
    marginBottom: 10 
  },
  author: { 
    color: Colors.text,
    fontWeight: '600'
  },
  content: { 
    color: Colors.accent,
    lineHeight: 20 
  },
  starContainer: {
    flexDirection: 'row'
  },
  categoryRatings: {
    backgroundColor: Colors.background,
    borderRadius: 8,
    padding: spacingScale.sm,
    marginBottom: spacingScale.sm,
  },
  categoryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacingScale.xs,
  },
  categoryLabel: {
    color: Colors.text,
    fontSize: 12,
    fontWeight: '500',
    flex: 1,
  },
});