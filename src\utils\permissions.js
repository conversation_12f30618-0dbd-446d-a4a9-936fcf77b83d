import * as Location from 'expo-location';
import * as ImagePicker from 'expo-image-picker';
import * as Camera from 'expo-camera';
import * as MediaLibrary from 'expo-media-library';
import * as Notifications from 'expo-notifications';
import * as Contacts from 'expo-contacts';
import { Alert, Linking, Platform } from 'react-native';

// Permission status constants
export const PERMISSION_STATUS = {
  GRANTED: 'granted',
  DENIED: 'denied',
  UNDETERMINED: 'undetermined',
  RESTRICTED: 'restricted'
};

// Permission types
export const PERMISSION_TYPES = {
  LOCATION: 'location',
  CAMERA: 'camera',
  MEDIA_LIBRARY: 'mediaLibrary',
  NOTIFICATIONS: 'notifications',
  CONTACTS: 'contacts',
  MICROPHONE: 'microphone'
};

// Check if permission is granted
export const isPermissionGranted = (status) => {
  return status === PERMISSION_STATUS.GRANTED;
};

// Location Permissions
export const requestLocationPermission = async () => {
  try {
    console.log('🗺️ Requesting location permission...');
    
    // Check current permission status
    const { status: existingStatus } = await Location.getForegroundPermissionsAsync();
    
    if (existingStatus === PERMISSION_STATUS.GRANTED) {
      console.log('✅ Location permission already granted');
      return { granted: true, status: existingStatus };
    }
    
    // Request permission
    const { status } = await Location.requestForegroundPermissionsAsync();
    
    if (status === PERMISSION_STATUS.GRANTED) {
      console.log('✅ Location permission granted');
      return { granted: true, status };
    } else {
      console.log('❌ Location permission denied');
      return { granted: false, status };
    }
  } catch (error) {
    console.error('❌ Error requesting location permission:', error);
    return { granted: false, error };
  }
};

// Camera Permissions
export const requestCameraPermission = async () => {
  try {
    console.log('📷 Requesting camera permission...');
    
    const { status: existingStatus } = await Camera.getCameraPermissionsAsync();
    
    if (existingStatus === PERMISSION_STATUS.GRANTED) {
      console.log('✅ Camera permission already granted');
      return { granted: true, status: existingStatus };
    }
    
    const { status } = await Camera.requestCameraPermissionsAsync();
    
    if (status === PERMISSION_STATUS.GRANTED) {
      console.log('✅ Camera permission granted');
      return { granted: true, status };
    } else {
      console.log('❌ Camera permission denied');
      return { granted: false, status };
    }
  } catch (error) {
    console.error('❌ Error requesting camera permission:', error);
    return { granted: false, error };
  }
};

// Media Library Permissions
export const requestMediaLibraryPermission = async () => {
  try {
    console.log('🖼️ Requesting media library permission...');
    
    const { status: existingStatus } = await MediaLibrary.getPermissionsAsync();
    
    if (existingStatus === PERMISSION_STATUS.GRANTED) {
      console.log('✅ Media library permission already granted');
      return { granted: true, status: existingStatus };
    }
    
    const { status } = await MediaLibrary.requestPermissionsAsync();
    
    if (status === PERMISSION_STATUS.GRANTED) {
      console.log('✅ Media library permission granted');
      return { granted: true, status };
    } else {
      console.log('❌ Media library permission denied');
      return { granted: false, status };
    }
  } catch (error) {
    console.error('❌ Error requesting media library permission:', error);
    return { granted: false, error };
  }
};

// Image Picker Permissions
export const requestImagePickerPermission = async () => {
  try {
    console.log('🖼️ Requesting image picker permission...');
    
    const { status: existingStatus } = await ImagePicker.getMediaLibraryPermissionsAsync();
    
    if (existingStatus === PERMISSION_STATUS.GRANTED) {
      console.log('✅ Image picker permission already granted');
      return { granted: true, status: existingStatus };
    }
    
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status === PERMISSION_STATUS.GRANTED) {
      console.log('✅ Image picker permission granted');
      return { granted: true, status };
    } else {
      console.log('❌ Image picker permission denied');
      return { granted: false, status };
    }
  } catch (error) {
    console.error('❌ Error requesting image picker permission:', error);
    return { granted: false, error };
  }
};

// Notification Permissions
export const requestNotificationPermission = async () => {
  try {
    console.log('🔔 Requesting notification permission...');
    
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    
    if (existingStatus.status === PERMISSION_STATUS.GRANTED) {
      console.log('✅ Notification permission already granted');
      return { granted: true, status: existingStatus.status };
    }
    
    const { status } = await Notifications.requestPermissionsAsync({
      ios: {
        allowAlert: true,
        allowBadge: true,
        allowSound: true,
        allowAnnouncements: true,
      },
    });
    
    if (status.status === PERMISSION_STATUS.GRANTED) {
      console.log('✅ Notification permission granted');
      return { granted: true, status: status.status };
    } else {
      console.log('❌ Notification permission denied');
      return { granted: false, status: status.status };
    }
  } catch (error) {
    console.error('❌ Error requesting notification permission:', error);
    return { granted: false, error };
  }
};

// Contacts Permissions
export const requestContactsPermission = async () => {
  try {
    console.log('👥 Requesting contacts permission...');
    
    const { status: existingStatus } = await Contacts.getPermissionsAsync();
    
    if (existingStatus === PERMISSION_STATUS.GRANTED) {
      console.log('✅ Contacts permission already granted');
      return { granted: true, status: existingStatus };
    }
    
    const { status } = await Contacts.requestPermissionsAsync();
    
    if (status === PERMISSION_STATUS.GRANTED) {
      console.log('✅ Contacts permission granted');
      return { granted: true, status };
    } else {
      console.log('❌ Contacts permission denied');
      return { granted: false, status };
    }
  } catch (error) {
    console.error('❌ Error requesting contacts permission:', error);
    return { granted: false, error };
  }
};

// Show permission denied alert
export const showPermissionDeniedAlert = (permissionType, onSettingsPress) => {
  const permissionNames = {
    [PERMISSION_TYPES.LOCATION]: 'Konum',
    [PERMISSION_TYPES.CAMERA]: 'Kamera',
    [PERMISSION_TYPES.MEDIA_LIBRARY]: 'Fotoğraf Galerisi',
    [PERMISSION_TYPES.NOTIFICATIONS]: 'Bildirimler',
    [PERMISSION_TYPES.CONTACTS]: 'Rehber',
    [PERMISSION_TYPES.MICROPHONE]: 'Mikrofon'
  };
  
  const permissionName = permissionNames[permissionType] || 'Bu özellik';
  
  Alert.alert(
    'İzin Gerekli',
    `${permissionName} erişimi bu özelliği kullanmak için gereklidir. Lütfen ayarlardan izin verin.`,
    [
      {
        text: 'İptal',
        style: 'cancel'
      },
      {
        text: 'Ayarlara Git',
        onPress: onSettingsPress || openAppSettings
      }
    ]
  );
};

// Open app settings
export const openAppSettings = () => {
  if (Platform.OS === 'ios') {
    Linking.openURL('app-settings:');
  } else {
    Linking.openSettings();
  }
};

// Check all permissions status
export const checkAllPermissions = async () => {
  try {
    const permissions = {};
    
    // Location
    const locationStatus = await Location.getForegroundPermissionsAsync();
    permissions.location = locationStatus.status;
    
    // Camera
    const cameraStatus = await Camera.getCameraPermissionsAsync();
    permissions.camera = cameraStatus.status;
    
    // Media Library
    const mediaLibraryStatus = await MediaLibrary.getPermissionsAsync();
    permissions.mediaLibrary = mediaLibraryStatus.status;
    
    // Notifications
    const notificationStatus = await Notifications.getPermissionsAsync();
    permissions.notifications = notificationStatus.status;
    
    // Contacts
    const contactsStatus = await Contacts.getPermissionsAsync();
    permissions.contacts = contactsStatus;
    
    console.log('📋 All permissions status:', permissions);
    return permissions;
  } catch (error) {
    console.error('❌ Error checking permissions:', error);
    return {};
  }
};

// Request multiple permissions
export const requestMultiplePermissions = async (permissionTypes) => {
  const results = {};
  
  for (const permissionType of permissionTypes) {
    switch (permissionType) {
      case PERMISSION_TYPES.LOCATION:
        results.location = await requestLocationPermission();
        break;
      case PERMISSION_TYPES.CAMERA:
        results.camera = await requestCameraPermission();
        break;
      case PERMISSION_TYPES.MEDIA_LIBRARY:
        results.mediaLibrary = await requestMediaLibraryPermission();
        break;
      case PERMISSION_TYPES.NOTIFICATIONS:
        results.notifications = await requestNotificationPermission();
        break;
      case PERMISSION_TYPES.CONTACTS:
        results.contacts = await requestContactsPermission();
        break;
      default:
        console.warn(`Unknown permission type: ${permissionType}`);
    }
  }
  
  return results;
};

export default {
  PERMISSION_STATUS,
  PERMISSION_TYPES,
  isPermissionGranted,
  requestLocationPermission,
  requestCameraPermission,
  requestMediaLibraryPermission,
  requestImagePickerPermission,
  requestNotificationPermission,
  requestContactsPermission,
  showPermissionDeniedAlert,
  openAppSettings,
  checkAllPermissions,
  requestMultiplePermissions
};
