import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import spacingScale from '../constants/spacing';

export default function EmptyState({ 
  icon = 'cafe-outline',
  title = 'Burada Henüz Bir Şey Yok',
  subtitle = '<PERSON>lk içeriğ<PERSON> sen ekle<PERSON>ye ne dersin?',
  actionText,
  onActionPress,
  style
}) {
  return (
    <View style={[styles.container, style]}>
      <View style={styles.iconContainer}>
        <Ionicons 
          name={icon} 
          size={80} 
          color={Colors.tabIconDefault} 
          style={styles.icon}
        />
      </View>
      
      <Text style={[styles.title, globalStyles.subheading]}>
        {title}
      </Text>
      
      <Text style={[styles.subtitle, globalStyles.body]}>
        {subtitle}
      </Text>
      
      {actionText && onActionPress && (
        <TouchableOpacity 
          style={styles.actionButton} 
          onPress={onActionPress}
          activeOpacity={0.7}
        >
          <Text style={[styles.actionText, globalStyles.body]}>
            {actionText}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacingScale.xl,
    paddingVertical: spacingScale.xxl,
  },
  iconContainer: {
    marginBottom: spacingScale.lg,
    opacity: 0.6,
  },
  icon: {
    opacity: 0.8,
  },
  title: {
    color: Colors.text,
    textAlign: 'center',
    marginBottom: spacingScale.sm,
  },
  subtitle: {
    color: Colors.tabIconDefault,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: spacingScale.xl,
  },
  actionButton: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingHorizontal: spacingScale.lg,
    paddingVertical: spacingScale.md,
  },
  actionText: {
    color: Colors.text,
    fontWeight: '600',
  },
});
