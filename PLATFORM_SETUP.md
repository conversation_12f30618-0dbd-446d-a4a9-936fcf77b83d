# SipTracker - Platform Configuration Guide

This document outlines the complete Android and iOS platform configuration for the SipTracker React Native app.

## 📱 Project Overview

**SipTracker** is a React Native app built with Expo managed workflow that allows users to track and discover amazing venues, rate their experiences, and find their next favorite spots.

### Tech Stack
- **Framework**: React Native 0.79.3 with Expo SDK 53
- **Architecture**: New Architecture enabled
- **Backend**: Supabase
- **Navigation**: React Navigation 7
- **Animations**: React Native Reanimated 3
- **UI**: Custom components with premium UX patterns

## 🔧 Platform Configurations

### Android Configuration

#### Package Information
- **Package Name**: `com.siptracker.app`
- **Version Code**: 1
- **Target SDK**: 34 (Android 14)
- **Min SDK**: 23 (Android 6.0)
- **Compile SDK**: 34

#### Permissions
The app requests the following Android permissions:
- `ACCESS_COARSE_LOCATION` - Basic location access
- `ACCESS_FINE_LOCATION` - Precise location access
- `CAMERA` - Camera access for profile photos
- `READ_EXTERNAL_STORAGE` - Read media files
- `WRITE_EXTERNAL_STORAGE` - Write media files
- `READ_MEDIA_IMAGES` - Read images (Android 13+)
- `READ_MEDIA_VIDEO` - Read videos (Android 13+)
- `RECORD_AUDIO` - Audio recording
- `VIBRATE` - Haptic feedback
- `INTERNET` - Network access
- `ACCESS_NETWORK_STATE` - Network state monitoring
- `ACCESS_WIFI_STATE` - WiFi state monitoring
- `READ_CONTACTS` - Contact access (optional)
- `USE_BIOMETRIC` - Biometric authentication
- `USE_FINGERPRINT` - Fingerprint authentication

#### Features
- **Adaptive Icon**: Configured with foreground image and white background
- **Edge-to-Edge**: Enabled for modern Android experience
- **Deep Linking**: Configured for `https://siptracker.app` and `https://www.siptracker.app`
- **Backup**: Allowed for user data backup

### iOS Configuration

#### Bundle Information
- **Bundle Identifier**: `com.siptracker.app`
- **Build Number**: 1
- **Deployment Target**: iOS 13.0+ (inferred from Expo SDK 53)
- **Device Support**: iPhone and iPad
- **Interface Style**: Automatic (supports dark mode)

#### Privacy Permissions (Info.plist)
All permission descriptions are in Turkish:
- `NSLocationWhenInUseUsageDescription` - Location access for nearby venues
- `NSLocationAlwaysAndWhenInUseUsageDescription` - Location access for nearby venues
- `NSCameraUsageDescription` - Camera access for profile photos
- `NSPhotoLibraryUsageDescription` - Photo library access for profile photos
- `NSMicrophoneUsageDescription` - Microphone access for audio recording
- `NSContactsUsageDescription` - Contact access to find friends
- `NSCalendarsUsageDescription` - Calendar access for event creation
- `NSRemindersUsageDescription` - Reminders access for notifications
- `NSFaceIDUsageDescription` - Face ID for secure authentication

#### Features
- **Universal Links**: Configured for `siptracker.app` and `www.siptracker.app`
- **Localization**: Supports Turkish (tr) and English (en)
- **Encryption**: Non-exempt encryption declared
- **Tablet Support**: Full iPad support enabled

## 🚀 Build Configuration

### EAS Build Profiles

#### Development Profile
- **Purpose**: Development and testing
- **Distribution**: Internal
- **iOS**: Uses simulator builds
- **Android**: Generates APK files
- **Development Client**: Enabled

#### Preview Profile
- **Purpose**: Internal testing and QA
- **Distribution**: Internal
- **iOS**: Device builds with simulator support
- **Android**: Release APK builds

#### Production Profile
- **Purpose**: App Store/Play Store releases
- **iOS**: App Store builds
- **Android**: AAB (Android App Bundle) builds

### Environment Variables

Create a `.env` file with the following variables:
```env
# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# Google Maps API Key
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# App Configuration
APP_ENV=development
API_BASE_URL=https://your-api-domain.com

# Deep Linking
DEEP_LINK_SCHEME=siptracker
DEEP_LINK_HOST=siptracker.app
```

## 📦 Installed Expo Plugins

The following Expo plugins are configured:

### Core Plugins
- `expo-font` - Custom font loading
- `expo-location` - Location services
- `expo-image-picker` - Image selection and camera
- `expo-notifications` - Push notifications
- `expo-camera` - Camera functionality
- `expo-media-library` - Media library access
- `expo-splash-screen` - Splash screen management
- `expo-updates` - Over-the-air updates
- `expo-linking` - Deep linking support

### Additional Utilities
- `expo-constants` - App constants and configuration
- `expo-device` - Device information
- `expo-application` - Application information
- `expo-crypto` - Cryptographic functions
- `expo-secure-store` - Secure storage

## 🔐 Security Configuration

### iOS Security
- **App Transport Security**: Configured for HTTPS
- **Keychain Access**: Enabled for secure storage
- **Face ID/Touch ID**: Configured for biometric authentication
- **Code Signing**: Ready for distribution certificates

### Android Security
- **Network Security**: HTTPS enforced
- **Biometric Authentication**: Fingerprint and face unlock support
- **ProGuard/R8**: Ready for code obfuscation in production
- **App Signing**: Ready for Play Store signing

## 🌐 Deep Linking Configuration

### URL Schemes
- **Custom Scheme**: `siptracker://`
- **Universal Links**: 
  - `https://siptracker.app/*`
  - `https://www.siptracker.app/*`

### Supported Paths
- `/venue/{id}` - Venue details
- `/user/{id}` - User profiles
- `/share/{type}` - Shared content

## 📱 Asset Configuration

### App Icons
- **iOS**: `icon.png` (1024x1024)
- **Android**: `adaptive-icon.png` (foreground) + white background
- **Web**: `favicon.png`

### Splash Screen
- **Image**: `splash-icon.png`
- **Background**: White (#ffffff)
- **Resize Mode**: Contain

## 🔧 Development Setup

### Prerequisites
1. Node.js 18+
2. Expo CLI
3. EAS CLI
4. Xcode (for iOS development)
5. Android Studio (for Android development)

### Installation
```bash
# Install dependencies
npm install

# Install EAS CLI
npm install -g @expo/eas-cli

# Login to Expo
eas login

# Configure EAS project
eas build:configure
```

### Building

#### Development Build
```bash
# iOS
eas build --profile development --platform ios

# Android
eas build --profile development --platform android
```

#### Production Build
```bash
# iOS
eas build --profile production --platform ios

# Android
eas build --profile production --platform android
```

## 📋 Pre-Deployment Checklist

### iOS App Store
- [ ] Update bundle identifier in app.json
- [ ] Configure Apple Developer account
- [ ] Set up App Store Connect app
- [ ] Update EAS credentials
- [ ] Test on physical devices
- [ ] Prepare app metadata and screenshots

### Google Play Store
- [ ] Update package name in app.json
- [ ] Configure Google Play Console
- [ ] Set up app signing key
- [ ] Update EAS credentials
- [ ] Test on various Android devices
- [ ] Prepare store listing and assets

## 🐛 Troubleshooting

### Common Issues
1. **Permission Denied**: Check Info.plist descriptions and Android permissions
2. **Build Failures**: Verify EAS configuration and credentials
3. **Deep Links Not Working**: Check URL scheme configuration
4. **Location Not Working**: Verify location permissions and API keys

### Support
- **Documentation**: [Expo Documentation](https://docs.expo.dev/)
- **Community**: [Expo Discord](https://discord.gg/expo)
- **Issues**: Create issues in the project repository

## 📄 License

This project is private and proprietary. All rights reserved.
