import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Dimensions,
  Pressable,
  ActivityIndicator,
  Vibration,
  Platform
} from 'react-native';
import Slider from '@react-native-community/slider';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInDown,
  FadeInUp,
  SlideInDown,
  SlideOutDown,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
  Easing
} from 'react-native-reanimated';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import spacingScale from '../constants/spacing';

const { width, height } = Dimensions.get('window');

// Memoized weight labels to prevent recalculation
const WEIGHT_LABELS = {
  0: 'Hiç <PERSON>nemli Değil',
  1: '<PERSON><PERSON>',
  2: '<PERSON><PERSON><PERSON>',
  3: 'Or<PERSON>',
  4: '<PERSON>nemli',
  5: 'Çok Önemli'
};

// Enhanced WeightSlider component with premium animations and haptic feedback
const WeightSlider = React.memo(({ title, icon, value, onValueChange, color, index }) => {
  const scaleValue = useSharedValue(1);
  const thumbScale = useSharedValue(1);
  const progressWidth = useSharedValue(0);
  const glowOpacity = useSharedValue(0);

  const animatedThumbStyle = useAnimatedStyle(() => ({
    transform: [{ scale: thumbScale.value }],
  }));

  const animatedProgressStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value}%`,
  }));

  const animatedGlowStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
    shadowOpacity: glowOpacity.value * 0.5,
  }));

  const handleValueChange = useCallback((newValue) => {
    // Haptic feedback for premium feel
    if (Platform.OS === 'ios') {
      Vibration.vibrate(10);
    }

    // Sophisticated animations
    thumbScale.value = withSequence(
      withTiming(1.4, { duration: 100, easing: Easing.out(Easing.quad) }),
      withTiming(1, { duration: 200, easing: Easing.out(Easing.back(1.5)) })
    );

    // Progress bar animation
    progressWidth.value = withTiming((newValue / 5) * 100, {
      duration: 300,
      easing: Easing.out(Easing.cubic)
    });

    // Glow effect on interaction
    glowOpacity.value = withSequence(
      withTiming(1, { duration: 150 }),
      withTiming(0, { duration: 400 })
    );

    onValueChange(newValue);
  }, [onValueChange, thumbScale, progressWidth, glowOpacity]);

  React.useEffect(() => {
    // Initialize progress bar
    progressWidth.value = withDelay(
      index * 100,
      withTiming((value / 5) * 100, {
        duration: 600,
        easing: Easing.out(Easing.cubic)
      })
    );
  }, []);

  const weightLabel = useMemo(() => WEIGHT_LABELS[value] || 'Orta', [value]);

  return (
    <Animated.View
      entering={SlideInDown.delay(index * 100).springify().damping(15)}
      style={styles.sliderContainer}
    >
      <View style={styles.sliderHeader}>
        <Text style={styles.sliderIcon}>{icon}</Text>
        <Text style={[styles.sliderTitle, globalStyles.body]}>{title}</Text>
        <Animated.View style={[
          styles.valueChip,
          { backgroundColor: color },
          animatedThumbStyle,
          animatedGlowStyle
        ]}>
          <Text style={styles.valueChipText}>{value}</Text>
        </Animated.View>
      </View>

      <View style={styles.sliderRow}>
        {/* Custom progress track */}
        <View style={styles.progressTrack}>
          <Animated.View
            style={[
              styles.progressFill,
              { backgroundColor: color },
              animatedProgressStyle
            ]}
          />
        </View>

        <Slider
          style={styles.slider}
          minimumValue={0}
          maximumValue={5}
          step={1}
          value={value}
          onValueChange={handleValueChange}
          minimumTrackTintColor="transparent"
          maximumTrackTintColor="transparent"
          thumbStyle={{
            backgroundColor: color,
            width: 28,
            height: 28,
            borderRadius: 14,
            elevation: 4,
            shadowColor: color,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.4,
            shadowRadius: 4,
            borderWidth: 3,
            borderColor: 'white',
          }}
        />
      </View>

      <Text style={[styles.weightLabel, globalStyles.bodySmall]}>
        {weightLabel}
      </Text>
    </Animated.View>
  );
});

export default function ExploreWizard({ visible, onClose, onApplyFilters }) {
  // Optimized state management - single state object to reduce re-renders
  const [weights, setWeights] = useState({
    coffee: 3,
    vibe: 3,
    service: 3
  });
  const [isApplying, setIsApplying] = useState(false);

  const scale = useSharedValue(0);
  const backdropOpacity = useSharedValue(0);
  const slideY = useSharedValue(height);
  const headerScale = useSharedValue(0.8);
  const buttonScale = useSharedValue(0.9);

  React.useEffect(() => {
    if (visible) {
      // Sophisticated entrance animation sequence
      backdropOpacity.value = withTiming(1, { duration: 300 });
      slideY.value = withSpring(0, {
        damping: 20,
        stiffness: 150,
        mass: 1
      });

      // Staggered header animation
      headerScale.value = withDelay(200, withSpring(1, {
        damping: 15,
        stiffness: 200
      }));

      // Button entrance animation
      buttonScale.value = withDelay(400, withSpring(1, {
        damping: 12,
        stiffness: 180
      }));
    } else {
      // Smooth exit animation
      slideY.value = withTiming(height * 0.5, {
        duration: 250,
        easing: Easing.in(Easing.cubic)
      });
      backdropOpacity.value = withTiming(0, { duration: 200 });
      headerScale.value = withTiming(0.8, { duration: 200 });
      buttonScale.value = withTiming(0.9, { duration: 200 });
    }
  }, [visible]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: slideY.value }],
  }));

  const backdropStyle = useAnimatedStyle(() => ({
    opacity: backdropOpacity.value,
  }));

  const headerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: headerScale.value }],
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: buttonScale.value }],
  }));

  // Enhanced handlers with loading states and animations
  const handleApply = useCallback(async () => {
    setIsApplying(true);

    // Button press animation
    buttonScale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );

    // Haptic feedback
    if (Platform.OS === 'ios') {
      Vibration.vibrate(50);
    }

    // Simulate processing time for premium feel
    await new Promise(resolve => setTimeout(resolve, 800));

    onApplyFilters(weights);
    setIsApplying(false);
    onClose();
  }, [weights, onApplyFilters, onClose, buttonScale]);

  const handleReset = useCallback(() => {
    // Reset animation with haptic feedback
    if (Platform.OS === 'ios') {
      Vibration.vibrate(30);
    }

    setWeights({
      coffee: 3,
      vibe: 3,
      service: 3
    });
  }, []);

  // Optimized weight change handlers
  const handleCoffeeChange = useCallback((value) => {
    setWeights(prev => ({ ...prev, coffee: value }));
  }, []);

  const handleVibeChange = useCallback((value) => {
    setWeights(prev => ({ ...prev, vibe: value }));
  }, []);

  const handleServiceChange = useCallback((value) => {
    setWeights(prev => ({ ...prev, service: value }));
  }, []);

  const handleBackdropPress = useCallback(() => {
    onClose();
  }, [onClose]);



  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <Pressable style={styles.overlay} onPress={handleBackdropPress}>
        <Animated.View style={[styles.backdrop, backdropStyle]} />
        <Animated.View
          style={[styles.container, animatedStyle]}
          onStartShouldSetResponder={() => true}
        >
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
            bounces={false}
          >
            {/* Enhanced Header with Premium Animation */}
            <Animated.View
              style={[styles.header, headerAnimatedStyle]}
            >
              <View style={styles.headerTop}>
                <Text style={[styles.title, globalStyles.subheading]}>
                  🔮 Keşfet Sihirbazı
                </Text>
                <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                  <Ionicons name="close" size={20} color={Colors.tabIconDefault} />
                </TouchableOpacity>
              </View>
              <Text style={[styles.subtitle, globalStyles.bodySmall]}>
                Önceliklerini belirle, sana uygun mekanları keşfet
              </Text>
            </Animated.View>

            {/* Enhanced Sliders with Staggered Animation */}
            <View style={styles.slidersContainer}>
              <WeightSlider
                title="Kahve Lezzeti"
                icon="☕"
                value={weights.coffee}
                onValueChange={handleCoffeeChange}
                color="#8B4513"
                index={0}
              />

              <WeightSlider
                title="Mekan Atmosferi"
                icon="🏠"
                value={weights.vibe}
                onValueChange={handleVibeChange}
                color="#FF6B6B"
                index={1}
              />

              <WeightSlider
                title="Servis Kalitesi"
                icon="👥"
                value={weights.service}
                onValueChange={handleServiceChange}
                color="#4ECDC4"
                index={2}
              />
            </View>

            {/* Premium Action Buttons with Loading States */}
            <Animated.View
              style={[styles.buttonContainer, buttonAnimatedStyle]}
            >
              <TouchableOpacity
                style={styles.resetButton}
                onPress={handleReset}
                activeOpacity={0.7}
                disabled={isApplying}
              >
                <Ionicons name="refresh" size={16} color={Colors.tabIconDefault} />
                <Text style={[styles.resetText, globalStyles.caption]}>
                  Sıfırla
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.applyButton,
                  isApplying && styles.applyButtonLoading
                ]}
                onPress={handleApply}
                activeOpacity={0.8}
                disabled={isApplying}
              >
                {isApplying ? (
                  <>
                    <ActivityIndicator size="small" color="white" />
                    <Text style={[styles.applyText, globalStyles.bodySmall]}>
                      Keşfediliyor...
                    </Text>
                  </>
                ) : (
                  <>
                    <Ionicons name="search" size={18} color="white" />
                    <Text style={[styles.applyText, globalStyles.bodySmall]}>
                      Keşfet
                    </Text>
                  </>
                )}
              </TouchableOpacity>
            </Animated.View>
          </ScrollView>
        </Animated.View>
      </Pressable>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  container: {
    backgroundColor: Colors.card,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    width: width,
    maxHeight: height * 0.75,
    elevation: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -5 },
    shadowOpacity: 0.3,
    shadowRadius: 15,
  },
  scrollContent: {
    padding: spacingScale.lg,
    paddingBottom: spacingScale.xl,
  },
  header: {
    marginBottom: spacingScale.lg,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacingScale.xs,
  },
  closeButton: {
    padding: spacingScale.xs,
    borderRadius: 16,
    backgroundColor: Colors.background,
  },
  title: {
    color: Colors.text,
    fontWeight: 'bold',
  },
  subtitle: {
    color: Colors.tabIconDefault,
    lineHeight: 18,
  },
  slidersContainer: {
    marginBottom: spacingScale.lg,
  },
  sliderContainer: {
    marginBottom: spacingScale.lg,
    backgroundColor: Colors.background,
    borderRadius: 16,
    padding: spacingScale.md,
  },
  sliderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacingScale.sm,
  },
  sliderIcon: {
    fontSize: 20,
    marginRight: spacingScale.sm,
  },
  sliderTitle: {
    color: Colors.text,
    fontWeight: '600',
    flex: 1,
    fontSize: 15,
  },
  valueChip: {
    minWidth: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  valueChipText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  sliderRow: {
    marginBottom: spacingScale.xs,
    position: 'relative',
  },
  progressTrack: {
    position: 'absolute',
    top: 18,
    left: 0,
    right: 0,
    height: 4,
    backgroundColor: Colors.background,
    borderRadius: 2,
    zIndex: 1,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  slider: {
    width: '100%',
    height: 40,
    zIndex: 2,
  },
  weightLabel: {
    color: Colors.tabIconDefault,
    textAlign: 'center',
    fontStyle: 'italic',
    fontSize: 12,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: spacingScale.md,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacingScale.md,
    paddingVertical: spacingScale.sm,
    borderRadius: 20,
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.tabIconDefault + '30',
  },
  resetText: {
    color: Colors.tabIconDefault,
    marginLeft: spacingScale.xs,
  },
  applyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: spacingScale.lg,
    paddingVertical: spacingScale.md,
    borderRadius: 24,
    flex: 1,
    justifyContent: 'center',
    elevation: 3,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  applyButtonLoading: {
    backgroundColor: Colors.primary + 'CC',
  },
  applyText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: spacingScale.xs,
  },
});
