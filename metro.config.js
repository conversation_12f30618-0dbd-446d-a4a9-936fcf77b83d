const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add support for additional file extensions
config.resolver.assetExts.push(
  // Fonts
  'ttf',
  'otf',
  'woff',
  'woff2',
  // Images (svg excluded - handled as source file)
  'png',
  'jpg',
  'jpeg',
  'gif',
  'webp',
  // Audio
  'mp3',
  'wav',
  'aac',
  'm4a',
  // Video
  'mp4',
  'mov',
  'avi',
  'mkv',
  // Documents
  'pdf',
  // <PERSON><PERSON> animations
  'json'
);

// Configure source extensions
config.resolver.sourceExts.push(
  'jsx',
  'js',
  'ts',
  'tsx',
  'json',
  'svg'
);

// Configure transformer for SVG files
config.transformer.babelTransformerPath = require.resolve('react-native-svg-transformer');

// Add platform-specific extensions
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

// Configure minification for production builds
if (process.env.NODE_ENV === 'production') {
  config.transformer.minifierConfig = {
    mangle: {
      keep_fnames: true,
    },
    output: {
      ascii_only: true,
      quote_keys: true,
      wrap_iife: true,
    },
    sourceMap: {
      includeSources: false,
    },
    toplevel: false,
    warnings: false,
  };
}

module.exports = config;
