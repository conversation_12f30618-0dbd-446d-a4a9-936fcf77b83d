import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import spacingScale from '../constants/spacing';

export default function VenueCard({ venue, navigation, showMatchScore = false }) {
  const handlePress = () => {
    navigation.navigate('VenueDetail', { venueId: venue.id });
  };

  return (
    <TouchableOpacity onPress={handlePress} activeOpacity={0.8}>
      <View style={styles.card}>
        <Image 
          source={{ uri: venue.main_image_url || 'https://via.placeholder.com/400x200.png?text=No+Image' }} 
          style={styles.image} 
        />
        <View style={styles.infoContainer}>
          <Text style={[styles.name, globalStyles.subheading]}>{venue.name}</Text>
          <Text style={[styles.address, globalStyles.bodySmall]}>{venue.city}</Text>
        </View>
        <View style={styles.ratingContainer}>
          <Ionicons name="star" color="#FFD700" size={16} />
          <Text style={[styles.rating, globalStyles.bodySmall]}>{venue.overall_rating.toFixed(1)}</Text>
        </View>

        {/* Match Score Badge */}
        {showMatchScore && venue.matchScore && (
          <View style={styles.matchScoreContainer}>
            <Ionicons name="analytics" color="white" size={14} />
            <Text style={[styles.matchScore, globalStyles.bodySmall]}>
              Uyum: %{Math.round(venue.matchScore)}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: { 
    backgroundColor: Colors.card, 
    borderRadius: 16, 
    marginHorizontal: 20, 
    marginBottom: 20 
  },
  image: { 
    width: '100%', 
    height: 180, 
    borderTopLeftRadius: 16, 
    borderTopRightRadius: 16 
  },
  infoContainer: { 
    padding: 16 
  },
  name: { 
    color: Colors.text, 
    marginBottom: 4 
  },
  address: { 
    color: Colors.tabIconDefault 
  },
  ratingContainer: { 
    position: 'absolute', 
    top: 12, 
    right: 12, 
    flexDirection: 'row', 
    alignItems: 'center', 
    backgroundColor: 'rgba(0,0,0,0.6)', 
    paddingHorizontal: 8, 
    paddingVertical: 4, 
    borderRadius: 12 
  },
  rating: {
    color: '#FFFFFF',
    marginLeft: 4,
    fontWeight: '600'
  },
  matchScoreContainer: {
    position: 'absolute',
    bottom: spacingScale.md,
    right: spacingScale.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: spacingScale.sm,
    paddingVertical: spacingScale.xs,
    borderRadius: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  matchScore: {
    color: 'white',
    marginLeft: spacingScale.xs,
    fontWeight: '600',
    fontSize: 12,
  }
});