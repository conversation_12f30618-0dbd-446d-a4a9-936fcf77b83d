-- SipTracker Database Güvenlik ve Düzeltme Komutları
-- <PERSON>u komutları Supabase SQL Editor'de çalıştırın

-- 1. PROFILES TABLOSU İÇİN RLS AKTIF ET
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 2. VENUES TABLOSU İÇİN RLS AKTIF ET
ALTER TABLE public.venues ENABLE ROW LEVEL SECURITY;

-- 3. REVIEWS TABLOSU İÇİN RLS AKTIF ET
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;

-- 4. PROFILES TABLOSU RLS POLİTİKALARI
-- <PERSON>llanı<PERSON><PERSON>lar sadece kendi profillerini görebilir ve düzenleyebilir
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 5. VENUES TABLOSU RLS POLİTİKALARI
-- Tüm kullanıcılar mekanları görebilir
CREATE POLICY "Anyone can view venues" ON public.venues
    FOR SELECT USING (true);

-- 6. REVIEWS TABLOSU RLS POLİTİKALARI
-- Tüm kullanıcılar yorumları görebilir
CREATE POLICY "Anyone can view reviews" ON public.reviews
    FOR SELECT USING (true);

-- Kullanıcılar sadece kendi yorumlarını ekleyebilir
CREATE POLICY "Users can insert own reviews" ON public.reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Kullanıcılar sadece kendi yorumlarını güncelleyebilir
CREATE POLICY "Users can update own reviews" ON public.reviews
    FOR UPDATE USING (auth.uid() = user_id);

-- Kullanıcılar sadece kendi yorumlarını silebilir
CREATE POLICY "Users can delete own reviews" ON public.reviews
    FOR DELETE USING (auth.uid() = user_id);

-- 7. EKSIK FOREIGN KEY CONSTRAINT EKLE
ALTER TABLE public.reviews 
ADD CONSTRAINT reviews_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;

-- 8. OTOMATIK PROFIL OLUŞTURMA TRIGGER'I
-- Kullanıcı kaydolduğunda otomatik profil oluştur
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, updated_at)
  VALUES (new.id, new.raw_user_meta_data->>'full_name', now());
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger'ı oluştur
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- 9. TEST VERİLERİ EKLE (Reviews tablosu için)
INSERT INTO public.reviews (venue_id, user_id, rating, content, author_name) VALUES
(1, (SELECT id FROM auth.users LIMIT 1), 5.0, 'Harika bir kahve deneyimi! Özellikle V60 demlemelerini tavsiye ederim.', 'Test Kullanıcı'),
(2, (SELECT id FROM auth.users LIMIT 1), 4.5, 'Çalışmak için mükemmel bir ortam. WiFi hızı da çok iyi.', 'Test Kullanıcı'),
(3, (SELECT id FROM auth.users LIMIT 1), 4.0, 'Kahveleri lezzetli ama biraz pahalı. Yine de tavsiye ederim.', 'Test Kullanıcı');

-- 10. MEVCUT KULLANICI İÇİN PROFİL OLUŞTUR (eğer yoksa)
INSERT INTO public.profiles (id, full_name, updated_at)
SELECT id, raw_user_meta_data->>'full_name', now()
FROM auth.users 
WHERE id NOT IN (SELECT id FROM public.profiles);
