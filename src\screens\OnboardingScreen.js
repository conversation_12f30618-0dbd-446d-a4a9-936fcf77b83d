import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withDelay,
  interpolate,
  Extrapolate
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import { toast } from '../utils/toast';
import spacingScale from '../constants/spacing';

const COFFEE_PREFERENCES = [
  'Meyvemsi', 'Çikolatamsı', 'Fındıklı', 'Düşük Asidite', 
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ta <PERSON>v<PERSON>', 'Koyu Kavrum'
];

const USAGE_PURPOSES = [
  { id: 'study', label: 'Ders <PERSON>ış<PERSON>', icon: '📚' },
  { id: 'taste', label: 'Lezzet <PERSON>şfi', icon: '☕' },
  { id: 'social', label: 'Sosyalleşmek', icon: '🤝' },
  { id: 'work', label: 'Çalışmak', icon: '💻' },
  { id: 'relax', label: 'Dinlenmek', icon: '🧘' },
  { id: 'date', label: 'Buluşmak', icon: '💕' }
];

export default function OnboardingScreen() {
  const [currentStep, setCurrentStep] = useState(0);
  const [username, setUsername] = useState('');
  const [selectedPreferences, setSelectedPreferences] = useState([]);
  const [selectedPurposeIds, setSelectedPurposeIds] = useState([]); // Changed to store IDs
  const [loading, setLoading] = useState(false);

  // Animation values
  const step1Opacity = useSharedValue(0);
  const step2Opacity = useSharedValue(0);
  const step3Opacity = useSharedValue(0);
  const step1TranslateY = useSharedValue(50);
  const step2TranslateY = useSharedValue(50);
  const step3TranslateY = useSharedValue(50);

  useEffect(() => {
    step1Opacity.value = withTiming(1, { duration: 600 });
    step1TranslateY.value = withTiming(0, { duration: 600 });
  }, []);

  useEffect(() => {
    if (currentStep >= 1) {
      step2Opacity.value = withDelay(200, withTiming(1, { duration: 600 }));
      step2TranslateY.value = withDelay(200, withTiming(0, { duration: 600 }));
    }
    if (currentStep >= 2) {
      step3Opacity.value = withDelay(400, withTiming(1, { duration: 600 }));
      step3TranslateY.value = withDelay(400, withTiming(0, { duration: 600 }));
    }
  }, [currentStep]);

  const step1Style = useAnimatedStyle(() => ({
    opacity: step1Opacity.value,
    transform: [{ translateY: step1TranslateY.value }],
  }));

  const step2Style = useAnimatedStyle(() => ({
    opacity: step2Opacity.value,
    transform: [{ translateY: step2TranslateY.value }],
  }));

  const step3Style = useAnimatedStyle(() => ({
    opacity: step3Opacity.value,
    transform: [{ translateY: step3TranslateY.value }],
  }));

  const togglePreference = (preference) => {
    setSelectedPreferences(prev => 
      prev.includes(preference) 
        ? prev.filter(p => p !== preference)
        : [...prev, preference]
    );
  };

  const togglePurpose = (purposeId) => {
    setSelectedPurposeIds(prev => 
      prev.includes(purposeId) 
        ? prev.filter(p => p !== purposeId)
        : [...prev, purposeId]
    );
  };

  const handleNext = () => {
    if (currentStep === 0 && username.trim()) {
      setCurrentStep(1);
    } else if (currentStep === 1 && selectedPreferences.length > 0) {
      setCurrentStep(2);
    }
  };

  const validateOnboardingData = () => {
    if (!username.trim()) {
      toast.warning('Eksik Bilgi', 'Lütfen bir kullanıcı adı girin.');
      return false;
    }

    if (username.trim().length < 3) {
      toast.error('Geçersiz Kullanıcı Adı', 'Kullanıcı adı en az 3 karakter olmalıdır.');
      return false;
    }

    if (!/^[a-zA-Z0-9_]+$/.test(username.trim())) {
      toast.error('Geçersiz Kullanıcı Adı', 'Kullanıcı adı sadece harf, rakam ve alt çizgi içerebilir.');
      return false;
    }

    if (selectedPreferences.length === 0) {
      toast.warning('Eksik Bilgi', 'Lütfen en az bir kahve tercihi seçin.');
      return false;
    }

    if (selectedPurposeIds.length === 0) {
      toast.warning('Eksik Bilgi', 'Lütfen en az bir kullanım amacı seçin.');
      return false;
    }

    return true;
  };

  const getOnboardingErrorMessage = (error) => {
    if (error.code === '23505') {
      return 'Bu kullanıcı adı zaten alınmış. Lütfen başka bir kullanıcı adı deneyin.';
    }
    if (error.code === 'PGRST301') {
      return 'Profil oluşturulamadı. Lütfen tekrar giriş yapın.';
    }
    return `Profil güncellenirken hata oluştu: ${error.message}`;
  };

  const handleComplete = async () => {
    if (!validateOnboardingData()) {
      return;
    }

    setLoading(true);
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError) {
        console.error('User fetch error:', userError);
        toast.error('Oturum Hatası', 'Kullanıcı oturumu doğrulanamadı. Lütfen tekrar giriş yapın.');
        return;
      }

      if (!user) {
        toast.error('Oturum Hatası', 'Kullanıcı oturumu bulunamadı. Lütfen tekrar giriş yapın.');
        return;
      }

      console.log('Updating profile for user:', user.id);

      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          username: username.trim().toLowerCase(),
          coffee_preferences: selectedPreferences,
          usage_purpose: selectedPurposeIds,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (updateError) {
        console.error('Profile update error:', updateError);
        toast.error('Profil Güncellenemedi', getOnboardingErrorMessage(updateError));
        return;
      }

      console.log('Profile updated successfully');
      toast.success('Profil Tamamlandı!', 'Hoş geldiniz! Artık uygulamayı kullanabilirsiniz.');

      // Small delay to ensure database update is committed
      await new Promise(resolve => setTimeout(resolve, 500));

      // Trigger auth state change to refresh profile in AppNavigator
      // Use refreshSession to force the auth listener to fire
      console.log('🔄 Refreshing session to trigger profile refetch...');
      const { error: refreshError } = await supabase.auth.refreshSession();

      if (refreshError) {
        console.error('❌ Session refresh failed:', refreshError);
        // If refresh fails, the user might need to log in again
        toast.warning('Oturum Yenileme', 'Profil tamamlandı ancak sayfayı yenilemek için uygulamayı yeniden başlatın.');
      } else {
        console.log('✅ Session refreshed successfully - profile should be refetched');
      }

    } catch (error) {
      console.error('Unexpected onboarding error:', error);
      toast.error('Beklenmeyen Hata', 'Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={[styles.title, globalStyles.heading]}>Profilinizi Oluşturalım</Text>
          <Text style={[styles.subtitle, globalStyles.body]}>
            Size özel kahve deneyimi için birkaç bilgiye ihtiyacımız var
          </Text>
        </View>

      {/* Step 1: Username */}
      <Animated.View style={[styles.section, step1Style]}>
        <Text style={[styles.sectionTitle, globalStyles.subheading]}>Kullanıcı Adınız</Text>
        <TextInput
          style={[styles.input, globalStyles.body]}
          placeholder="Benzersiz bir kullanıcı adı seçin"
          placeholderTextColor={Colors.tabIconDefault}
          value={username}
          onChangeText={setUsername}
          autoCapitalize="none"
        />
        {username.trim() && currentStep === 0 && (
          <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
            <Text style={[styles.nextButtonText, globalStyles.body]}>Devam Et</Text>
          </TouchableOpacity>
        )}
      </Animated.View>

      {/* Step 2: Coffee Preferences */}
      {currentStep >= 1 && (
        <Animated.View style={[styles.section, step2Style]}>
          <Text style={[styles.sectionTitle, globalStyles.subheading]}>Favori Tatlarınız</Text>
          <Text style={[styles.sectionSubtitle, globalStyles.bodySmall]}>
            Hangi kahve tatlarını seviyorsunuz? (Birden fazla seçebilirsiniz)
          </Text>
          <View style={styles.tagsContainer}>
            {COFFEE_PREFERENCES.map((preference) => (
              <TouchableOpacity
                key={preference}
                style={[
                  styles.tag,
                  selectedPreferences.includes(preference) && styles.tagSelected
                ]}
                onPress={() => togglePreference(preference)}
              >
                <Text style={[
                  styles.tagText,
                  globalStyles.bodySmall,
                  selectedPreferences.includes(preference) && styles.tagTextSelected
                ]}>
                  {preference}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          {selectedPreferences.length > 0 && currentStep === 1 && (
            <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
              <Text style={[styles.nextButtonText, globalStyles.body]}>Devam Et</Text>
            </TouchableOpacity>
          )}
        </Animated.View>
      )}

      {/* Step 3: Usage Purpose */}
      {currentStep >= 2 && (
        <Animated.View style={[styles.section, step3Style]}>
          <Text style={[styles.sectionTitle, globalStyles.subheading]}>Kullanım Amacınız</Text>
          <Text style={[styles.sectionSubtitle, globalStyles.bodySmall]}>
            Uygulamayı ne için kullanacaksınız?
          </Text>
          <View style={styles.cardsContainer}>
            {USAGE_PURPOSES.map((purpose) => (
              <TouchableOpacity
                key={purpose.id}
                style={[
                  styles.purposeCard,
                  selectedPurposeIds.includes(purpose.id) && styles.purposeCardSelected
                ]}
                onPress={() => togglePurpose(purpose.id)}
              >
                <Text style={styles.purposeIcon}>{purpose.icon}</Text>
                <Text style={[
                  styles.purposeText,
                  globalStyles.bodySmall,
                  selectedPurposeIds.includes(purpose.id) && styles.purposeTextSelected
                ]}>
                  {purpose.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          
          {selectedPurposeIds.length > 0 && (
            <TouchableOpacity 
              style={[styles.completeButton, loading && styles.buttonDisabled]}
              onPress={handleComplete}
              disabled={loading}
            >
              {loading ? (
                <>
                  <ActivityIndicator color={Colors.text} size="small" />
                  <Text style={[styles.completeButtonText, globalStyles.body, { marginLeft: 8 }]}>
                    Profil Oluşturuluyor...
                  </Text>
                </>
              ) : (
                <Text style={[styles.completeButtonText, globalStyles.body]}>
                  Profili Tamamla
                </Text>
              )}
            </TouchableOpacity>
          )}
        </Animated.View>
      )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: spacingScale.xl,
    paddingVertical: spacingScale.xl,
    paddingBottom: spacingScale.xl + 80, // Extra padding for safe area
  },
  header: {
    marginBottom: 40,
    alignItems: 'center',
  },
  title: {
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    color: Colors.tabIconDefault,
    textAlign: 'center',
    lineHeight: 24,
  },
  section: {
    marginBottom: 40,
  },
  sectionTitle: {
    color: Colors.text,
    marginBottom: 8,
  },
  sectionSubtitle: {
    color: Colors.tabIconDefault,
    marginBottom: 20,
    lineHeight: 20,
  },
  input: {
    backgroundColor: Colors.card,
    color: Colors.text,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginBottom: 20,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  tag: {
    backgroundColor: Colors.card,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  tagSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  tagText: {
    color: Colors.text,
  },
  tagTextSelected: {
    color: Colors.background,
  },
  cardsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  purposeCard: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    width: '47%',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  purposeCardSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  purposeIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  purposeText: {
    color: Colors.text,
    textAlign: 'center',
  },
  purposeTextSelected: {
    color: Colors.background,
  },
  nextButton: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  nextButtonText: {
    color: Colors.text,
    fontWeight: '600',
  },
  completeButton: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    marginTop: 20,
  },
  completeButtonText: {
    color: Colors.text,
    fontWeight: '600',
  },
  buttonDisabled: {
    opacity: 0.7,
  },
});