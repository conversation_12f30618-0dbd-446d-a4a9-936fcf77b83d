import Constants from 'expo-constants';
import { Platform } from 'react-native';

// App Configuration
export const APP_CONFIG = {
  // App Information
  name: 'SipTracker',
  version: '1.0.0',
  buildNumber: 1,
  
  // Environment
  environment: __DEV__ ? 'development' : 'production',
  
  // Platform Information
  platform: Platform.OS,
  isIOS: Platform.OS === 'ios',
  isAndroid: Platform.OS === 'android',
  
  // App Store Information
  bundleId: {
    ios: 'com.siptracker.app',
    android: 'com.siptracker.app'
  },
  
  // Deep Linking
  scheme: 'siptracker',
  universalLinks: {
    domain: 'siptracker.app',
    paths: ['/venue/*', '/user/*', '/share/*']
  },
  
  // API Configuration
  api: {
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000
  },
  
  // Feature Flags
  features: {
    locationServices: true,
    pushNotifications: true,
    socialLogin: true,
    biometricAuth: true,
    darkMode: true,
    analytics: true,
    crashReporting: true
  },
  
  // UI Configuration
  ui: {
    primaryColor: '#6366f1',
    secondaryColor: '#8b5cf6',
    accentColor: '#06b6d4',
    errorColor: '#ef4444',
    warningColor: '#f59e0b',
    successColor: '#10b981',
    
    // Animation Durations
    animations: {
      fast: 200,
      normal: 300,
      slow: 500
    },
    
    // Spacing System
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
      xxl: 48
    },
    
    // Border Radius
    borderRadius: {
      sm: 4,
      md: 8,
      lg: 12,
      xl: 16,
      full: 9999
    }
  },
  
  // Storage Keys
  storage: {
    userToken: '@siptracker:userToken',
    userProfile: '@siptracker:userProfile',
    preferences: '@siptracker:preferences',
    onboardingComplete: '@siptracker:onboardingComplete',
    biometricEnabled: '@siptracker:biometricEnabled'
  },
  
  // Notification Categories
  notifications: {
    categories: {
      venue: 'venue_updates',
      social: 'social_activity',
      system: 'system_alerts'
    }
  },
  
  // Location Configuration
  location: {
    accuracy: 'high',
    distanceInterval: 10,
    timeInterval: 5000,
    enableHighAccuracy: true,
    timeout: 15000,
    maximumAge: 10000
  },
  
  // Image Configuration
  images: {
    quality: 0.8,
    maxWidth: 1024,
    maxHeight: 1024,
    allowsEditing: true,
    aspect: [1, 1]
  },
  
  // Social Media Links
  social: {
    website: 'https://siptracker.app',
    support: 'https://siptracker.app/support',
    privacy: 'https://siptracker.app/privacy',
    terms: 'https://siptracker.app/terms',
    twitter: 'https://twitter.com/siptracker',
    instagram: 'https://instagram.com/siptracker'
  }
};

// Get app version info
export const getAppVersion = () => {
  return {
    version: Constants.expoConfig?.version || APP_CONFIG.version,
    buildNumber: Constants.expoConfig?.ios?.buildNumber || 
                 Constants.expoConfig?.android?.versionCode || 
                 APP_CONFIG.buildNumber,
    nativeVersion: Constants.nativeAppVersion,
    nativeBuildVersion: Constants.nativeBuildVersion
  };
};

// Check if feature is enabled
export const isFeatureEnabled = (feature) => {
  return APP_CONFIG.features[feature] || false;
};

// Get platform-specific bundle ID
export const getBundleId = () => {
  return Platform.select({
    ios: APP_CONFIG.bundleId.ios,
    android: APP_CONFIG.bundleId.android,
    default: APP_CONFIG.bundleId.ios
  });
};

export default APP_CONFIG;
