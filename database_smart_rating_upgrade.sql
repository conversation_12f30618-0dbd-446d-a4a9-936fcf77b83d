-- SipTracker Akıllı Puanlama Sistemi Veritabanı Yükseltmesi
-- Bu komutları Supabase SQL Editor'de çalıştırın

-- 1. VENUES TABLOSUNA KATEGORİ BAZLI ORTALAMA PUANLAR EKLE
ALTER TABLE public.venues 
ADD COLUMN rating_coffee numeric(2,1) DEFAULT 3.0,
ADD COLUMN rating_vibe numeric(2,1) DEFAULT 3.0,
ADD COLUMN rating_service numeric(2,1) DEFAULT 3.0,
ADD COLUMN reviews_count integer DEFAULT 0;

-- 2. REVIEWS TABLOSUNA KATEGORİ BAZLI PUANLAR EKLE
ALTER TABLE public.reviews 
ADD COLUMN rating_coffee numeric(2,1),
ADD COLUMN rating_vibe numeric(2,1),
ADD COLUMN rating_service numeric(2,1);

-- 3. MEVCUT REVIEWS İÇİN VARSAYILAN DEĞERLER ATAYIN
-- Mevcut tek rating değerini tüm kategorilere eşit olarak dağıt
UPDATE public.reviews 
SET 
  rating_coffee = rating,
  rating_vibe = rating,
  rating_service = rating
WHERE rating_coffee IS NULL;

-- 4. OTOMATIK ORTALAMA GÜNCELLEME FONKSİYONU
CREATE OR REPLACE FUNCTION public.update_venue_ratings()
RETURNS trigger AS $$
DECLARE
  venue_id_val bigint;
  coffee_avg numeric;
  vibe_avg numeric;
  service_avg numeric;
  review_count integer;
BEGIN
  -- Hangi venue_id ile çalışacağımızı belirle
  IF TG_OP = 'DELETE' THEN
    venue_id_val := OLD.venue_id;
  ELSE
    venue_id_val := NEW.venue_id;
  END IF;

  -- Bu venue için tüm kategorilerin ortalamalarını hesapla
  SELECT 
    COALESCE(AVG(rating_coffee), 3.0),
    COALESCE(AVG(rating_vibe), 3.0),
    COALESCE(AVG(rating_service), 3.0),
    COUNT(*)
  INTO coffee_avg, vibe_avg, service_avg, review_count
  FROM public.reviews 
  WHERE venue_id = venue_id_val;

  -- Venues tablosunu güncelle
  UPDATE public.venues 
  SET 
    rating_coffee = coffee_avg,
    rating_vibe = vibe_avg,
    rating_service = service_avg,
    reviews_count = review_count,
    overall_rating = (coffee_avg + vibe_avg + service_avg) / 3.0
  WHERE id = venue_id_val;

  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. TRIGGER'LARI OLUŞTUR
DROP TRIGGER IF EXISTS update_venue_ratings_trigger ON public.reviews;

CREATE TRIGGER update_venue_ratings_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.reviews
  FOR EACH ROW EXECUTE FUNCTION public.update_venue_ratings();

-- 6. MEVCUT VERİLER İÇİN İLK HESAPLAMA
-- Tüm venue'lar için rating'leri yeniden hesapla
DO $$
DECLARE
  venue_record RECORD;
BEGIN
  FOR venue_record IN SELECT DISTINCT venue_id FROM public.reviews LOOP
    PERFORM public.update_venue_ratings() FROM public.reviews WHERE venue_id = venue_record.venue_id LIMIT 1;
  END LOOP;
END $$;

-- 7. MEVCUT VENUES İÇİN VARSAYILAN DEĞERLER
UPDATE public.venues 
SET 
  rating_coffee = COALESCE(rating_coffee, overall_rating, 3.0),
  rating_vibe = COALESCE(rating_vibe, overall_rating, 3.0),
  rating_service = COALESCE(rating_service, overall_rating, 3.0),
  reviews_count = COALESCE(reviews_count, 0)
WHERE rating_coffee IS NULL OR rating_vibe IS NULL OR rating_service IS NULL;

-- 8. CONSTRAINT'LER EKLE
ALTER TABLE public.venues 
ADD CONSTRAINT check_rating_coffee CHECK (rating_coffee >= 1.0 AND rating_coffee <= 5.0),
ADD CONSTRAINT check_rating_vibe CHECK (rating_vibe >= 1.0 AND rating_vibe <= 5.0),
ADD CONSTRAINT check_rating_service CHECK (rating_service >= 1.0 AND rating_service <= 5.0);

ALTER TABLE public.reviews 
ADD CONSTRAINT check_review_rating_coffee CHECK (rating_coffee >= 1.0 AND rating_coffee <= 5.0),
ADD CONSTRAINT check_review_rating_vibe CHECK (rating_vibe >= 1.0 AND rating_vibe <= 5.0),
ADD CONSTRAINT check_review_rating_service CHECK (rating_service >= 1.0 AND rating_service <= 5.0);

-- 9. TEST VERİLERİ EKLE (Opsiyonel)
-- Mevcut venue'lara örnek detaylı puanlar ekle
INSERT INTO public.reviews (venue_id, user_id, rating, rating_coffee, rating_vibe, rating_service, content, author_name) VALUES
(1, (SELECT id FROM auth.users LIMIT 1), 4.5, 5.0, 4.0, 4.5, 'Kahveleri mükemmel, atmosfer güzel ama servis biraz yavaş.', 'Test Kullanıcı 2'),
(2, (SELECT id FROM auth.users LIMIT 1), 4.0, 3.5, 5.0, 3.5, 'Çalışmak için harika bir ortam, kahve ortalama.', 'Test Kullanıcı 3')
ON CONFLICT DO NOTHING;

-- 10. VERİ TUTARLILIĞI KONTROLÜ
-- Tüm rating'lerin doğru hesaplandığını kontrol et
SELECT 
  v.name,
  v.rating_coffee,
  v.rating_vibe, 
  v.rating_service,
  v.overall_rating,
  v.reviews_count,
  (SELECT COUNT(*) FROM public.reviews WHERE venue_id = v.id) as actual_review_count
FROM public.venues v
ORDER BY v.id;
